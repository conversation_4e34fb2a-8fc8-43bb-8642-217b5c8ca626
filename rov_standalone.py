#!/usr/bin/env python3
"""
ROV水下仿真 - Standalone模式
使用Python类替代ActionGraph实现ROV仿真
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

import os
import numpy as np
import omni.usd
from omni.isaac.core import World
from omni.isaac.core.utils.stage import add_reference_to_stage
from omni.isaac.core.utils.prims import get_prim_at_path
from omni.isaac.core.prims import RigidPrimView
import carb

# 导入我们的ROV类
from rov_classes import (
    BuoyancyForcesNode,
    BuoyancyControlNode, 
    DampingNode,
    ControllerNode,
    LinearAngularControlNode,
    QuatToEulerNode
)


class ROVSimulation:
    """ROV仿真主类"""
    
    def __init__(self):
        """初始化ROV仿真"""
        self.world = None
        self.rov_prim = None
        self.rov_view = None
        
        # 初始化ROV计算节点
        self.buoyancy_forces_node = BuoyancyForcesNode("BuoyancyForces")
        self.buoyancy_control_node = BuoyancyControlNode("BuoyancyControl")
        self.damping_node = DampingNode("Damping")
        self.controller_node = ControllerNode("Controller")
        self.thruster_control_node = LinearAngularControlNode("ThrusterControl")
        self.quat_to_euler_node = QuatToEulerNode("QuatToEuler")
        
        # ROV参数
        self.rov_volume = 0.1  # m³
        self.rov_height = 0.2  # m
        
        # 控制输入
        self.joystick_x = 0.0
        self.joystick_y = 0.0
        self.dive_input = 0.0
        
    def setup_world(self):
        """设置仿真世界"""
        self.world = World(stage_units_in_meters=1.0)
        
    def load_usd_scene(self, usd_path: str):
        """
        加载USD场景文件
        
        Args:
            usd_path: USD文件的绝对路径
        """
        if not os.path.exists(usd_path):
            carb.log_error(f"USD文件不存在: {usd_path}")
            return False
            
        try:
            # 使用omni.usd.get_context().open_stage()打开USD文件
            context = omni.usd.get_context()
            context.open_stage(usd_path)
            carb.log_info(f"成功加载USD文件: {usd_path}")
            
            # 等待场景加载完成
            self.world.reset()
            
            return True
        except Exception as e:
            carb.log_error(f"加载USD文件失败: {e}")
            return False
    
    def setup_rov(self):
        """设置ROV对象"""
        try:
            # 获取ROV prim
            rov_prim_path = "/World/ROV"
            self.rov_prim = get_prim_at_path(rov_prim_path)
            
            if self.rov_prim is None:
                carb.log_error(f"未找到ROV对象: {rov_prim_path}")
                return False
            
            # 创建RigidPrimView用于物理操作
            self.rov_view = RigidPrimView(
                prim_paths_expr=rov_prim_path,
                name="rov_view"
            )
            
            carb.log_info("ROV设置完成")
            return True
            
        except Exception as e:
            carb.log_error(f"ROV设置失败: {e}")
            return False
    
    def get_rov_state(self):
        """获取ROV当前状态"""
        if self.rov_view is None:
            return None
            
        try:
            # 获取位置和旋转
            positions = self.rov_view.get_world_poses()[0]
            orientations = self.rov_view.get_world_poses()[1]
            
            if positions is not None and len(positions) > 0:
                position = positions[0]
                orientation = orientations[0]
                
                return {
                    'position': position,
                    'orientation': orientation,
                    'z_position': float(position[2])
                }
        except Exception as e:
            carb.log_error(f"获取ROV状态失败: {e}")
            
        return None
    
    def compute_forces(self, rov_state):
        """计算ROV受力"""
        if rov_state is None:
            return
        
        # 转换四元数到欧拉角
        self.quat_to_euler_node.set_inputs(
            quaternion=[
                float(rov_state['orientation'][0]),
                float(rov_state['orientation'][1]), 
                float(rov_state['orientation'][2]),
                float(rov_state['orientation'][3])
            ]
        )
        euler_output = self.quat_to_euler_node.execute()
        rotation = euler_output.get('rotation', [0.0, 0.0, 0.0])
        
        # 计算浮力（带旋转）
        self.buoyancy_forces_node.set_inputs(
            volume=self.rov_volume,
            height=self.rov_height,
            z_position=rov_state['z_position'],
            rotation=rotation
        )
        buoyancy_output = self.buoyancy_forces_node.execute()
        
        # 计算阻尼
        self.damping_node.set_inputs(
            z_position=rov_state['z_position'],
            max_damping=1.0,
            floating_obj_height=self.rov_height
        )
        damping_output = self.damping_node.execute()
        
        # 计算推进器控制
        self.thruster_control_node.set_inputs(
            y_stick=self.joystick_y,
            x_stick=self.joystick_x
        )
        thruster_output = self.thruster_control_node.execute()
        
        # 计算PID控制（使用pitch角度）
        pitch_angle = rotation[1] if len(rotation) > 1 else 0.0
        self.controller_node.set_inputs(
            orientation=pitch_angle,
            dive_force=self.dive_input
        )
        controller_output = self.controller_node.execute()
        
        return {
            'buoyancy': buoyancy_output,
            'damping': damping_output,
            'thruster': thruster_output,
            'controller': controller_output,
            'rotation': rotation
        }
    
    def apply_forces(self, forces):
        """应用计算出的力到ROV"""
        if self.rov_view is None or forces is None:
            return
            
        try:
            # 组合所有力
            total_force = np.array([0.0, 0.0, 0.0])
            
            # 添加浮力
            buoyancy = forces.get('buoyancy', {})
            total_force[0] += buoyancy.get('x_force', 0.0)
            total_force[1] += buoyancy.get('y_force', 0.0)
            total_force[2] += buoyancy.get('z_force', 0.0)
            
            # 添加推进器力（简化处理，实际应该分别应用到各个推进器）
            thruster = forces.get('thruster', {})
            left_front = thruster.get('left_front', [0, 0, 0])
            right_front = thruster.get('right_front', [0, 0, 0])
            left_back = thruster.get('left_back', [0, 0, 0])
            right_back = thruster.get('right_back', [0, 0, 0])
            
            # 合并推进器力
            thruster_force = np.array(left_front) + np.array(right_front) + \
                           np.array(left_back) + np.array(right_back)
            total_force += thruster_force
            
            # 添加控制器力
            controller = forces.get('controller', {})
            controller_force = controller.get('force', [0, 0, 0])
            total_force += np.array(controller_force)
            
            # 应用力到ROV
            forces_tensor = total_force.reshape(1, 3)
            self.rov_view.apply_forces(forces_tensor)
            
        except Exception as e:
            carb.log_error(f"应用力失败: {e}")
    
    def update_controls(self):
        """更新控制输入（这里可以添加键盘/手柄输入）"""
        # 简单的键盘控制示例
        # 实际使用中可以集成手柄输入
        pass
    
    def step(self):
        """仿真步进"""
        if self.world is None:
            return
            
        # 更新控制输入
        self.update_controls()
        
        # 获取ROV状态
        rov_state = self.get_rov_state()
        
        # 计算力
        forces = self.compute_forces(rov_state)
        
        # 应用力
        self.apply_forces(forces)
        
        # 步进仿真
        self.world.step(render=True)


def main():
    """主函数"""
    # 创建ROV仿真实例
    rov_sim = ROVSimulation()
    
    # 设置世界
    rov_sim.setup_world()
    
    # 加载USD场景
    usd_path = os.path.abspath("ROV_THRUSTERS.usd")
    if not rov_sim.load_usd_scene(usd_path):
        carb.log_error("无法加载USD场景，退出")
        simulation_app.close()
        return
    
    # 设置ROV
    if not rov_sim.setup_rov():
        carb.log_error("无法设置ROV，退出")
        simulation_app.close()
        return
    
    carb.log_info("ROV仿真启动成功，开始仿真循环...")
    
    # 仿真循环
    try:
        while simulation_app.is_running():
            rov_sim.step()
    except KeyboardInterrupt:
        carb.log_info("用户中断仿真")
    except Exception as e:
        carb.log_error(f"仿真错误: {e}")
    finally:
        simulation_app.close()


if __name__ == "__main__":
    main()
