#!/usr/bin/env python3
"""
简单的USD文件信息查看工具
不依赖<PERSON>，使用标准USD库
"""

import os
import sys

def analyze_usd_simple(usd_path: str):
    """简单分析USD文件"""
    
    if not os.path.exists(usd_path):
        print(f"❌ USD文件不存在: {usd_path}")
        return
    
    print(f"📁 USD文件: {usd_path}")
    print(f"📊 文件大小: {os.path.getsize(usd_path) / 1024:.1f} KB")
    
    # 尝试使用标准USD库
    try:
        from pxr import Usd, UsdGeom, UsdPhysics
        
        # 打开USD文件
        stage = Usd.Stage.Open(usd_path)
        
        if not stage:
            print("❌ 无法打开USD文件")
            return
        
        print("\n" + "="*60)
        print("USD场景结构分析")
        print("="*60)
        
        # 遍历所有prim
        all_prims = []
        rov_prims = []
        
        for prim in stage.Traverse():
            prim_path = str(prim.GetPath())
            prim_type = prim.GetTypeName()
            
            all_prims.append((prim_path, prim_type))
            
            if "/World/ROV" in prim_path or "ROV" in prim_path:
                rov_prims.append((prim_path, prim_type))
        
        print(f"\n📊 统计信息:")
        print(f"  总prim数量: {len(all_prims)}")
        print(f"  ROV相关对象: {len(rov_prims)}")
        
        # 显示场景层次结构
        print(f"\n📁 场景结构 (前3级):")
        for prim_path, prim_type in all_prims:
            level = prim_path.count('/') - 1
            if level <= 3:
                indent = "  " * level
                print(f"{indent}├─ {prim_path} ({prim_type})")
        
        # 详细分析ROV对象
        if rov_prims:
            print(f"\n🤖 ROV相关对象详情:")
            for prim_path, prim_type in rov_prims:
                print(f"  📍 {prim_path}")
                print(f"     类型: {prim_type}")
                
                # 获取prim
                prim = stage.GetPrimAtPath(prim_path)
                if prim:
                    # 检查是否有RigidBodyAPI
                    if prim.HasAPI(UsdPhysics.RigidBodyAPI):
                        print(f"     ✅ 具有RigidBodyAPI")
                    else:
                        print(f"     ❌ 没有RigidBodyAPI")
                    
                    # 检查是否有CollisionAPI
                    if prim.HasAPI(UsdPhysics.CollisionAPI):
                        print(f"     ✅ 具有CollisionAPI")
                    else:
                        print(f"     ❌ 没有CollisionAPI")
                    
                    # 检查几何类型
                    if prim.IsA(UsdGeom.Mesh):
                        print(f"     🔺 几何类型: Mesh")
                    elif prim.IsA(UsdGeom.Cube):
                        print(f"     📦 几何类型: Cube")
                    elif prim.IsA(UsdGeom.Sphere):
                        print(f"     🔵 几何类型: Sphere")
                    elif prim.IsA(UsdGeom.Xform):
                        print(f"     📐 几何类型: Transform")
                    else:
                        print(f"     ❓ 几何类型: 其他")
                
                print()
        
        # 推荐路径
        print("🎯 推荐的刚体路径:")
        rigid_paths = []
        for prim_path, prim_type in rov_prims:
            prim = stage.GetPrimAtPath(prim_path)
            if prim and prim.HasAPI(UsdPhysics.RigidBodyAPI):
                rigid_paths.append(prim_path)
                print(f"  ✅ {prim_path}")
        
        if not rigid_paths:
            print("  ❌ 未找到具有RigidBodyAPI的对象")
            print("  💡 建议检查以下路径:")
            for prim_path, prim_type in rov_prims:
                if prim_type in ["Mesh", "Cube", "Sphere", "Xform"]:
                    print(f"     - {prim_path}")
        
    except ImportError:
        print("❌ 无法导入USD库 (pxr)")
        print("💡 尝试基本文件分析...")
        
        # 基本文件内容分析
        try:
            with open(usd_path, 'rb') as f:
                content = f.read(1024).decode('utf-8', errors='ignore')
                
            print(f"\n📄 文件内容预览:")
            lines = content.split('\n')[:10]
            for i, line in enumerate(lines, 1):
                if line.strip():
                    print(f"  {i:2d}: {line[:60]}...")
            
            # 搜索关键词
            keywords = ['ROV', 'RigidBody', 'Physics', 'Collision']
            print(f"\n🔍 关键词搜索:")
            for keyword in keywords:
                if keyword in content:
                    print(f"  ✅ 找到: {keyword}")
                else:
                    print(f"  ❌ 未找到: {keyword}")
                    
        except Exception as e:
            print(f"❌ 文件读取失败: {e}")
    
    except Exception as e:
        print(f"❌ USD分析失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    # 使用用户的路径
    usd_path = "/home/<USER>/self_underwater_isaacsim/ROV_THRUSTERS.usd"
    
    print("🔍 USD文件结构分析工具")
    print("="*60)
    
    analyze_usd_simple(usd_path)
    
    print("\n💡 使用建议:")
    print("1. 如果找到具有RigidBodyAPI的对象，在rov_standalone.py中使用该路径")
    print("2. 如果没有找到，可能需要在Isaac Sim中为ROV对象添加物理属性")
    print("3. 确保ROV对象具有正确的碰撞体和刚体设置")


if __name__ == "__main__":
    main()
